<?php
require_once __DIR__ . '/utils/stripe_helper.php';

$sessionId = $_GET['session_id'] ?? null;
$sessionInfo = null;
$error = null;

if ($sessionId) {
    $result = StripeHelper::retrieveCheckoutSession($sessionId);
    if ($result['success']) {
        $sessionInfo = $result['session'];
    } else {
        $error = $result['error'];
    }
} else {
    $error = 'ID de sesión no proporcionado';
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pago Exitoso - FastlyAI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/custom.css">
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <?php if ($sessionInfo && $sessionInfo->payment_status === 'paid'): ?>
                <!-- Pago exitoso -->
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
                        ¡Pago Exitoso!
                    </h2>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        Tu compra de créditos se ha procesado correctamente
                    </p>
                </div>

                <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        Detalles de la compra
                    </h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Créditos comprados:</span>
                            <span class="font-semibold text-gray-900 dark:text-white">
                                <?php echo htmlspecialchars($sessionInfo->metadata->credits ?? 'N/A'); ?>
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Monto pagado:</span>
                            <span class="font-semibold text-gray-900 dark:text-white">
                                $<?php echo number_format($sessionInfo->amount_total / 100, 2); ?> USD
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">ID de transacción:</span>
                            <span class="font-mono text-sm text-gray-900 dark:text-white">
                                <?php echo htmlspecialchars(substr($sessionId, -12)); ?>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="text-center space-y-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Tus créditos han sido agregados a tu cuenta automáticamente.
                    </p>
                    
                    <div class="space-y-2">
                        <a href="index.php" 
                           class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Comenzar a usar créditos
                        </a>
                        
                        <a href="pricing.php" 
                           class="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Ver planes de créditos
                        </a>
                    </div>
                </div>

            <?php else: ?>
                <!-- Error o pago no completado -->
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
                        Error en el pago
                    </h2>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        <?php echo htmlspecialchars($error ?? 'No se pudo verificar el estado del pago'); ?>
                    </p>
                </div>

                <div class="text-center">
                    <a href="pricing.php" 
                       class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Intentar de nuevo
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Auto-redirect después de 10 segundos si el pago fue exitoso
        <?php if ($sessionInfo && $sessionInfo->payment_status === 'paid'): ?>
        setTimeout(() => {
            window.location.href = 'index.php';
        }, 10000);
        <?php endif; ?>
    </script>
</body>
</html>
