<div class="container mx-auto px-4">
    <div class="flex items-center justify-between h-16">
        <div class="flex items-center space-x-6">
            <a href="/" class="text-gray-800 font-bold text-lg dark:text-blue-400">FastlyAI</a>
            <!-- Enlaces de navegación visibles en pantallas medianas y grandes -->
            <div class="hidden md:flex md:space-x-6">
                <a href="/ejemplos.php" class="text-gray-600 hover:text-blue-600 font-medium transition-colors dark:text-gray-300 dark:hover:text-blue-400"><?php echo t('nav_examples'); ?></a>
                <a href="/como-usar.php" class="text-gray-600 hover:text-blue-600 font-medium transition-colors dark:text-gray-300 dark:hover:text-blue-400"><?php echo t('nav_how_to_use'); ?></a>
                <a href="/pricing.php" class="text-gray-600 hover:text-blue-600 font-medium transition-colors dark:text-gray-300 dark:hover:text-blue-400"><?php echo t('pricing_title'); ?></a>
                <a href="/politicas.php" class="text-gray-600 hover:text-blue-600 font-medium transition-colors dark:text-gray-300 dark:hover:text-blue-400"><?php echo t('nav_policies'); ?></a>
            </div>
        </div>
        <div class="flex items-center space-x-2 sm:space-x-4">
            <div class="flex space-x-3 mr-4 social-icons hidden sm:flex">
                <a href="https://www.instagram.com/jawuil.p" target="_blank" class="text-gray-500 hover:text-gray-700 transition-colors dark:text-gray-400 dark:hover:text-gray-300">
                    <svg class="w-5 h-5" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                    </svg>
                </a>

                <a href="https://x.com/PJawuil" target="_blank" class="text-gray-500 hover:text-gray-700 transition-colors dark:text-gray-400 dark:hover:text-gray-300">
                    <svg class="w-5 h-5" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                    </svg>
                </a>
                <a href="https://www.threads.net/@jawuil.p" target="_blank" class="text-gray-500 hover:text-gray-700 transition-colors dark:text-gray-400 dark:hover:text-gray-300">
                    <svg class="w-5 h-5" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M12.186 24h-.007c-5.947-.024-10.371-2.794-10.371-10.371V10.371C1.808 4.424 6.232 1.63 12.179 1.63h.014c5.947.024 10.371 2.794 10.371 10.371v3.258c0 5.947-4.424 8.741-10.371 8.741h-.007zm-.007-20.541c-4.777 0-8.542 2.208-8.542 8.542v3.258c0 6.334 3.765 8.542 8.542 8.542h.007c4.777 0 8.542-2.208 8.542-8.542V10.371c0-6.334-3.765-8.542-8.542-8.542h-.007z"/>
                        <path d="M12 8.5c-1.933 0-3.5 1.567-3.5 3.5s1.567 3.5 3.5 3.5 3.5-1.567 3.5-3.5-1.567-3.5-3.5-3.5zm0 5.5c-1.103 0-2-.897-2-2s.897-2 2-2 2 .897 2 2-.897 2-2 2z"/>
                        <path d="M16.5 8.5c-.276 0-.5-.224-.5-.5s.224-.5.5-.5.5.224.5.5-.224.5-.5.5z"/>
                    </svg>
                </a>
            </div>
            <!-- Selector de idioma -->
            <div class="relative inline-block text-left hidden sm:block">
                <button id="language-selector" class="flex items-center space-x-1 text-gray-600 hover:text-blue-600 transition-colors dark:text-gray-300 dark:hover:text-blue-400">
                    <span class="text-sm font-medium"><?php echo $current_language === 'en' ? 'EN' : 'ES'; ?></span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="language-dropdown" class="hidden absolute right-0 mt-2 w-24 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 z-50">
                    <div class="py-1" role="menu" aria-orientation="vertical">
                        <button onclick="changeLanguage('es')" class="<?php echo $current_language === 'es' ? 'bg-gray-100 dark:bg-gray-600' : ''; ?> w-full text-left block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600" role="menuitem"><?php echo t('spanish'); ?></button>
                        <button onclick="changeLanguage('en')" class="<?php echo $current_language === 'en' ? 'bg-gray-100 dark:bg-gray-600' : ''; ?> w-full text-left block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600" role="menuitem"><?php echo t('english'); ?></button>
                    </div>
                </div>
            </div>

            <button id="toggle-dark-mode" class="hidden sm:flex bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors text-sm font-semibold dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600">
                <?php echo t('nav_dark_mode'); ?>
            </button>

            <!-- Indicador de carga de autenticación -->
            <div id="auth-loading" class="flex items-center space-x-2">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Cargando...</span>
            </div>

            <!-- Botones de autenticación (visibles cuando el usuario no está autenticado) -->
            <div id="auth-buttons" class="hidden flex space-x-3 auth-hidden">
                <button onclick="showLoginModal()" class="auth-button bg-blue-600 hover:bg-blue-700 text-white text-sm font-semibold py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105">
                    <?php echo t('nav_login'); ?>
                </button>
                <button onclick="showRegisterModal()" class="auth-button bg-white hover:bg-gray-50 text-gray-800 text-sm font-semibold py-2 px-4 rounded-lg border border-gray-300 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700">
                    <?php echo t('nav_signup'); ?>
                </button>
            </div>

            <!-- Botón de perfil de usuario (visible cuando el usuario está autenticado) -->
            <div id="user-profile-button" class="hidden relative auth-hidden">
                <style>
                    /* Estilos específicos para el dropdown del usuario */
                    #user-profile-dropdown {
                        min-width: 14rem;
                        max-width: 20rem;
                    }

                    /* Responsive adjustments */
                    @media (max-width: 640px) {
                        #user-profile-dropdown {
                            right: 0;
                            left: auto;
                            transform: translateX(0);
                        }
                    }

                    @media (min-width: 641px) {
                        #user-profile-dropdown {
                            right: 0;
                            left: auto;
                        }
                    }

                    /* Animación suave para el dropdown */
                    #user-profile-dropdown.hidden {
                        opacity: 0;
                        transform: translateY(-10px) scale(0.95);
                        transition: all 0.2s ease-out;
                        pointer-events: none;
                    }

                    #user-profile-dropdown:not(.hidden) {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                        transition: all 0.2s ease-out;
                        pointer-events: auto;
                    }

                    /* Animación de la flecha */
                    #user-profile-dropdown:not(.hidden) ~ button #dropdown-arrow {
                        transform: rotate(180deg);
                    }

                    /* Hover effect mejorado */
                    #user-profile-dropdown-button:hover {
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        transform: translateY(-1px);
                    }

                    /* Estilo para el avatar con borde */
                    #user-avatar, #user-avatar-fallback {
                        transition: all 0.2s ease;
                    }

                    /* Forzar z-index para que la imagen esté encima del fallback */
                    #user-avatar {
                        z-index: 10;
                    }

                    #user-avatar-fallback {
                        z-index: 5;
                    }

                    #dropdown-user-avatar {
                        z-index: 10;
                    }

                    #dropdown-user-avatar-fallback {
                        z-index: 5;
                    }

                    #user-profile-dropdown-button:hover #user-avatar,
                    #user-profile-dropdown-button:hover #user-avatar-fallback {
                        transform: scale(1.05);
                        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
                    }

                    /* Mejorar el texto del perfil */
                    #user-profile-dropdown-button .text-xs {
                        opacity: 0.8;
                        transition: opacity 0.2s ease;
                    }

                    #user-profile-dropdown-button:hover .text-xs {
                        opacity: 1;
                    }
                </style>
                <button id="user-profile-dropdown-button" class="flex items-center space-x-3 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 rounded-lg px-3 py-2 border border-gray-200 dark:border-gray-600 transition-all duration-200 shadow-sm hover:shadow-md">
                    <!-- Avatar siempre visible -->
                    <div class="relative w-8 h-8">
                        <img id="user-avatar" class="absolute inset-0 w-8 h-8 rounded-full object-cover border-2 border-blue-500" src="" alt="Avatar" style="display: block;">
                        <div id="user-avatar-fallback" class="absolute inset-0 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium border-2 border-blue-500"></div>
                    </div>
                    <!-- Información del usuario (oculta en móvil) -->
                    <div class="hidden sm:block">
                        <div class="text-left">
                            <p id="user-name" class="text-sm font-medium text-gray-900 dark:text-white"></p>
                            <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo t('view_profile'); ?></p>
                        </div>
                    </div>
                    <!-- Flecha -->
                    <svg class="w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform duration-200" id="dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <!-- Menú desplegable -->
                <div id="user-profile-dropdown" class="hidden absolute right-0 top-full mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 z-[9999] transform origin-top-right">
                    <div class="py-1">
                        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0 relative w-10 h-10">
                                    <img id="dropdown-user-avatar" class="absolute inset-0 w-10 h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600" src="" alt="Avatar" style="display: none;">
                                    <div id="dropdown-user-avatar-fallback" class="absolute inset-0 w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium border-2 border-gray-200 dark:border-gray-600"></div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p id="dropdown-user-name" class="text-sm font-semibold text-gray-900 dark:text-white truncate"></p>
                                    <p id="dropdown-user-email" class="text-xs text-gray-500 dark:text-gray-400 truncate"></p>
                                </div>
                            </div>
                        </div>
                        <div class="py-1">
                            <!-- Contador de créditos -->
                            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                        <span class="text-sm text-gray-600 dark:text-gray-400"><?php echo t('your_credits'); ?>:</span>
                                    </div>
                                    <span id="nav-user-credits" class="text-lg font-bold text-blue-600 dark:text-blue-400">0</span>
                                </div>
                                <a href="pricing.php" class="text-xs text-blue-600 dark:text-blue-400 hover:underline">
                                    <?php echo t('buy_credits'); ?>
                                </a>
                            </div>

                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <?php echo t('profile'); ?>
                            </a>
                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <?php echo t('settings'); ?>
                            </a>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-600">
                            <div class="py-1">
                                <button onclick="logout()" class="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-150">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    <?php echo t('logout'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="hidden md:block">
                <button data-tally-open="n9xMp4" data-tally-layout="modal" data-tally-emoji-text="👋" data-tally-emoji-animation="wave" class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-semibold py-2 px-4 rounded-lg transition-colors shadow-sm hover:shadow-md">
                    <?php echo t('nav_feedback'); ?>
                </button>
            </div>
            <!-- Botón de menú móvil -->
            <button id="mobile-menu-button" class="md:hidden flex items-center justify-center w-10 h-10 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 dark:text-gray-300 dark:hover:text-blue-400 dark:hover:bg-gray-700 transition-colors">
                <span class="sr-only">Abrir menú principal</span>
                <!-- Icono de menú (tres líneas) -->
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>
    </div>
    <!-- Agregar este botón donde corresponda en tu nav.php -->
</div>