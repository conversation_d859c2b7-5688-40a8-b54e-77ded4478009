/**
 * Funciones para manejar el cambio de idioma
 */

// Función para cambiar idioma sin query parameters
async function changeLanguage(lang) {
    try {
        console.log('🌐 Cambiando idioma a:', lang);

        // Guardar en localStorage
        localStorage.setItem('user_language', lang);

        // Establecer cookie para el servidor
        document.cookie = `user_language=${lang}; path=/; max-age=${86400 * 30}`;

        console.log('✅ Idioma cambiado exitosamente');

        // Recargar la página SIN query parameters para aplicar las traducciones
        window.location.href = window.location.origin + window.location.pathname;

    } catch (error) {
        console.error('💥 Error al cambiar idioma:', error);
    }
}

// Función para inicializar el idioma desde localStorage al cargar la página
function initializeLanguage() {
    const savedLang = localStorage.getItem('user_language');
    if (savedLang && ['es', 'en'].includes(savedLang)) {
        // Establecer cookie si hay idioma guardado en localStorage
        document.cookie = `user_language=${savedLang}; path=/; max-age=${86400 * 30}`;
        console.log('🌐 Idioma inicializado desde localStorage:', savedLang);
    }
}

// Inicializar el idioma cuando se carga la página
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguage();
    
    // Configurar el selector de idioma del nav principal
    const languageSelector = document.getElementById('language-selector');
    const languageDropdown = document.getElementById('language-dropdown');

    if (languageSelector && languageDropdown) {
        // Mostrar/ocultar el menú desplegable al hacer clic en el selector
        languageSelector.addEventListener('click', function(e) {
            e.stopPropagation();
            languageDropdown.classList.toggle('hidden');
        });

        // Cerrar el menú desplegable al hacer clic fuera de él
        document.addEventListener('click', function() {
            if (!languageDropdown.classList.contains('hidden')) {
                languageDropdown.classList.add('hidden');
            }
        });
    }
});
