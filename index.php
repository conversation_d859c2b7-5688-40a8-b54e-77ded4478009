<?php
// Incluir archivos necesarios
 require_once './include/language.php';
require_once './include/supabase_config.php';

// Obtener configuración de Supabase
$supabaseConfig = getSupabaseConfig();
?>
<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastlyAI - <?php echo t('site_title'); ?></title>

    <!-- ⚡ Script INLINE para evitar flash del modo oscuro -->
    <script>
        (function() {
            if (localStorage.getItem('darkMode') === 'enabled' ||
                (localStorage.getItem('darkMode') === null && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                document.documentElement.classList.add('dark');
            }
        })();
    </script>

    <!-- Script para aplicar el modo oscuro inmediatamente -->
    <script type="module" src="./js/colorMode.js"></script>
    <link rel="stylesheet" href="./css/styles.css">
    <link rel="stylesheet" href="./css/custom.css">
    <link rel="stylesheet" href="./css/slider.css">

    <?php  require './include/analytics.php'; ?>
    <script src="./js/modals.js"></script>
    <script async src="https://tally.so/widgets/embed.js"></script>

    <!-- Scripts de Supabase para autenticación -->
   <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js"></script>
   <script src="./js/auth.js"></script>
   <script src="./js/language.js"></script>
   <script src="./js/typewriter.js"></script>
   <script src="./js/forms.js"></script>
   <script src="./js/history.js"></script>
   <script src="./js/language.js"></script>
    <script>
      // Pasar las frases traducidas al JavaScript
      window.typewriterPhrases = <?php echo json_encode(t('typewriter_phrases')); ?>;

      // Pasar las traducciones del modo oscuro y formularios al JavaScript
      window.translations = {
        dark_mode_text: <?php echo json_encode(t('dark_mode_text')); ?>,
        light_mode_text: <?php echo json_encode(t('light_mode_text')); ?>,
        form_describe_project: <?php echo json_encode(t('form_describe_project')); ?>,
        form_describe_content: <?php echo json_encode(t('form_describe_content')); ?>,
        form_project_type_label: <?php echo json_encode(t('form_project_type_label')); ?>,
        form_project_type_placeholder: <?php echo json_encode(t('form_project_type_placeholder')); ?>,
        form_features_label: <?php echo json_encode(t('form_features_label')); ?>,
        form_features_placeholder: <?php echo json_encode(t('form_features_placeholder')); ?>,
        form_style_label: <?php echo json_encode(t('form_style_label')); ?>,
        form_style_placeholder: <?php echo json_encode(t('form_style_placeholder')); ?>,
        form_content_type_label: <?php echo json_encode(t('form_content_type_label')); ?>,
        form_content_type_placeholder: <?php echo json_encode(t('form_content_type_placeholder')); ?>,
        form_main_topic_label: <?php echo json_encode(t('form_main_topic_label')); ?>,
        form_main_topic_placeholder: <?php echo json_encode(t('form_main_topic_placeholder')); ?>,
        form_tone_label: <?php echo json_encode(t('form_tone_label')); ?>,
        form_tone_placeholder: <?php echo json_encode(t('form_tone_placeholder')); ?>,
        form_generate_prompt: <?php echo json_encode(t('form_generate_prompt')); ?>,
        form_back: <?php echo json_encode(t('form_back')); ?>
      };
    </script>
    <script>
      // Limpiar URL inmediatamente antes que cualquier otra cosa
      (function() {
        const url = new URL(window.location);
        if (url.searchParams.has('lang')) {
          url.searchParams.delete('lang');
          window.history.replaceState({}, document.title, url.pathname + url.search);
        }
      })();

      // Configuración de Supabase (se configurará después)
      let supabase = null;
      let currentUser = null;

      // Inicializar Supabase cuando se proporcionen las credenciales
      function initSupabase(url, anonKey) {
        console.log('🔧 Inicializando cliente de Supabase...');
        console.log('🌐 URL:', url);
        console.log('🔑 Clave anónima:', anonKey ? 'Presente' : 'Ausente');

        supabase = window.supabase.createClient(url, anonKey);

        console.log('✅ Cliente de Supabase inicializado');

        // Verificar si estamos regresando de una autenticación OAuth
        handleOAuthCallback();
      }

      // Manejar el callback de OAuth (cuando el usuario regresa de Google)
      async function handleOAuthCallback() {
        const urlParams = new URLSearchParams(window.location.search);
        const accessToken = urlParams.get('access_token');
        const refreshToken = urlParams.get('refresh_token');
        const error = urlParams.get('error');
        const errorDescription = urlParams.get('error_description');

        console.log('🔍 Verificando parámetros de URL:', {
          accessToken: accessToken ? 'Presente' : 'Ausente',
          refreshToken: refreshToken ? 'Presente' : 'Ausente',
          error: error,
          errorDescription: errorDescription,
          fullURL: window.location.href
        });

        if (error) {
          console.error('❌ Error en OAuth callback:', error, errorDescription);
          showErrorMessage('Error de autenticación: ' + error);
          return;
        }

        if (accessToken) {
          console.log('🔄 Procesando callback de OAuth...');
          console.log('🔑 Token de acceso recibido:', accessToken.substring(0, 20) + '...');

          // Limpiar la URL de los parámetros de OAuth
          const cleanUrl = window.location.origin + window.location.pathname;
          window.history.replaceState({}, document.title, cleanUrl);

          // Verificar el estado de autenticación después del callback
          console.log('⏳ Esperando 2 segundos antes de verificar estado...');
          setTimeout(async () => {
            console.log('🔄 Verificando estado después del callback...');
            await checkAuthState();
          }, 2000);
        } else {
          // Si no hay tokens en la URL, verificar si hay una sesión activa
          console.log('🔍 No hay tokens en URL, verificando sesión existente...');
          setTimeout(async () => {
            await checkAuthState();
          }, 500);
        }
      }

      // Verificar el estado de autenticación
      async function checkAuthState() {
        if (!supabase) {
          console.log('🔴 Supabase no está inicializado');
          return;
        }

        try {
          console.log('🔍 Verificando estado de autenticación...');

          // Método 1: Obtener usuario actual
          console.log('📋 Método 1: Obteniendo usuario con getUser()...');
          const { data: { user }, error: userError } = await supabase.auth.getUser();

          if (userError) {
            console.error('❌ Error al obtener usuario:', userError);
          } else if (user) {
            console.log('✅ Usuario obtenido con getUser():', user);
            console.log('� Email:', user.email);
            console.log('🆔 ID:', user.id);
            console.log('📱 Phone:', user.phone);
            console.log('� Provider:', user.app_metadata?.provider);
            console.log('👤 User Metadata:', user.user_metadata);
            console.log('⚙️ App Metadata:', user.app_metadata);
            console.log('📅 Creado:', user.created_at);
            console.log('🔄 Última vez visto:', user.last_sign_in_at);
          } else {
            console.log('❌ No hay usuario con getUser()');
          }

          // Método 2: Obtener sesión actual
          console.log('📋 Método 2: Obteniendo sesión con getSession()...');
          const { data: { session }, error: sessionError } = await supabase.auth.getSession();

          if (sessionError) {
            console.error('❌ Error al obtener sesión:', sessionError);
            updateUserInterface(false);
            return;
          }

          if (session) {
            console.log('✅ Sesión encontrada:', session);
            console.log('� Token de acceso:', session.access_token ? 'Presente (' + session.access_token.substring(0, 20) + '...)' : 'Ausente');
            console.log('🔄 Token de refresh:', session.refresh_token ? 'Presente (' + session.refresh_token.substring(0, 20) + '...)' : 'Ausente');
            console.log('⏰ Expira en:', new Date(session.expires_at * 1000));
            console.log('� Expires at (timestamp):', session.expires_at);

            const sessionUser = session.user;
            currentUser = sessionUser;

            console.log('👤 Usuario de la sesión:', sessionUser);
            console.log('📧 Email del usuario:', sessionUser.email);
            console.log('🆔 ID del usuario:', sessionUser.id);
            console.log('� Metadata del usuario:', sessionUser.user_metadata);
            console.log('⚙️ App metadata:', sessionUser.app_metadata);

            // Extraer información específica
            const userInfo = {
              id: sessionUser.id,
              email: sessionUser.email,
              full_name: sessionUser.user_metadata?.full_name,
              name: sessionUser.user_metadata?.name,
              given_name: sessionUser.user_metadata?.given_name,
              family_name: sessionUser.user_metadata?.family_name,
              picture: sessionUser.user_metadata?.picture,
              avatar_url: sessionUser.user_metadata?.avatar_url,
              provider: sessionUser.app_metadata?.provider,
              providers: sessionUser.app_metadata?.providers
            };

            console.log('📊 Información procesada del usuario:', userInfo);

            updateUserInterface(true, sessionUser);
          } else {
            console.log('❌ No hay sesión activa');
            currentUser = null;
            updateUserInterface(false);
          }
        } catch (error) {
          console.error('💥 Error al verificar autenticación:', error);
          updateUserInterface(false);
        }
      }

      // Función para refrescar la sesión del usuario
      async function refreshUserSession() {
        if (!supabase) return;

        try {
          console.log('🔄 Intentando refrescar sesión...');

          const { data, error } = await supabase.auth.refreshSession();

          if (error) {
            console.error('❌ Error al refrescar sesión:', error);
            return false;
          }

          if (data.session) {
            console.log('✅ Sesión refrescada exitosamente');
            console.log('👤 Usuario después del refresh:', data.session.user);
            currentUser = data.session.user;
            updateUserInterface(true, data.session.user);
            return true;
          }

          return false;
        } catch (error) {
          console.error('💥 Error inesperado al refrescar sesión:', error);
          return false;
        }
      }

      // Escuchar cambios en el estado de autenticación
      function setupAuthListener() {
        if (!supabase) {
          console.log('🔴 No se puede configurar el listener: Supabase no está inicializado');
          return;
        }

        console.log('👂 Configurando listener de autenticación...');

        supabase.auth.onAuthStateChange(async (event, session) => {
          console.log('🔄 Cambio de estado de autenticación:', event);

          if (session) {
            console.log('📊 Datos de la sesión:', {
              user_id: session.user?.id,
              email: session.user?.email,
              expires_at: new Date(session.expires_at * 1000),
              token_present: !!session.access_token
            });
          }

          currentUser = session?.user || null;

          switch (event) {
            case 'SIGNED_IN':
              console.log('✅ Usuario ha iniciado sesión');
              console.log('👤 Datos completos del usuario al iniciar sesión:', session.user);

              updateUserInterface(true, session.user);
              closeAuthModal(); // Cerrar modal al iniciar sesión
              break;
            case 'SIGNED_OUT':
              console.log('👋 Usuario ha cerrado sesión');
              updateUserInterface(false);
              break;
            case 'TOKEN_REFRESHED':
              console.log('🔄 Token renovado');
              break;
            default:
              console.log('ℹ️ Evento de autenticación:', event);
          }
        });
      }

      // Función para actualizar la interfaz según el estado de autenticación
      function updateUserInterface(isAuthenticated, user = null) {
        console.log('🎨 Actualizando interfaz de usuario:', { isAuthenticated, user: user ? 'presente' : 'ausente' });

        const authButtons = document.getElementById('auth-buttons');
        const mobileAuthButtons = document.getElementById('mobile-auth-buttons');
        const userProfileButton = document.getElementById('user-profile-button');
        const mobileUserProfile = document.getElementById('mobile-user-profile');

        if (isAuthenticated && user) {
          console.log('👤 Configurando interfaz para usuario autenticado');

          // Obtener información del usuario con más opciones
          const email = user.email || '';
          const fullName = user.user_metadata?.full_name || user.user_metadata?.name || '';
          const firstName = user.user_metadata?.given_name || '';
          const lastName = user.user_metadata?.family_name || '';

          // Determinar el nombre a mostrar
          let displayName = fullName;
          if (!displayName && firstName) {
            displayName = firstName + (lastName ? ' ' + lastName : '');
          }
          if (!displayName) {
            displayName = email.split('@')[0];
          }

          // Generar iniciales
          const nameParts = displayName.split(' ').filter(part => part.length > 0);
          const initials = nameParts.length >= 2
            ? (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase()
            : displayName.substring(0, 2).toUpperCase();

          console.log('📝 Información procesada del usuario:', {
            email,
            displayName,
            initials,
            metadata: user.user_metadata
          });

          // Actualizar elementos de la interfaz
          const userAvatar = document.getElementById('user-avatar');
          const userName = document.getElementById('user-name');
          const mobileUserAvatar = document.getElementById('mobile-user-avatar');
          const mobileUserName = document.getElementById('mobile-user-name');

          if (userAvatar) {
            userAvatar.textContent = initials;
            console.log('✅ Avatar actualizado:', initials);
          }
          if (userName) {
            userName.textContent = displayName;
            console.log('✅ Nombre actualizado:', displayName);
          }
          if (mobileUserAvatar) mobileUserAvatar.textContent = initials;
          if (mobileUserName) mobileUserName.textContent = displayName;

          // Mostrar perfil de usuario y ocultar botones de autenticación
          if (userProfileButton) userProfileButton.classList.remove('hidden');
          if (mobileUserProfile) mobileUserProfile.classList.remove('hidden');
          if (authButtons) authButtons.style.display = 'none';
          if (mobileAuthButtons) mobileAuthButtons.style.display = 'none';

          console.log('✅ Interfaz configurada para usuario autenticado');
        } else {
          console.log('🚫 Configurando interfaz para usuario no autenticado');

          // Mostrar botones de autenticación y ocultar perfil de usuario
          if (userProfileButton) userProfileButton.classList.add('hidden');
          if (mobileUserProfile) mobileUserProfile.classList.add('hidden');
          if (authButtons) authButtons.style.display = 'flex';
          if (mobileAuthButtons) mobileAuthButtons.style.display = 'block';

          console.log('✅ Interfaz configurada para usuario no autenticado');
        }
      }

      // Función para mostrar el modal de inicio de sesión
      function showLoginModal() {
        const modal = document.getElementById('auth-modal');
        const loginForm = document.getElementById('login-form');
        const registerForm = document.getElementById('register-form');

        if (modal && loginForm && registerForm) {
          loginForm.classList.remove('hidden');
          registerForm.classList.add('hidden');
          modal.classList.remove('hidden');
        }
      }

      // Función para mostrar el modal de registro
      function showRegisterModal() {
        const modal = document.getElementById('auth-modal');
        const loginForm = document.getElementById('login-form');
        const registerForm = document.getElementById('register-form');

        if (modal && loginForm && registerForm) {
          registerForm.classList.remove('hidden');
          loginForm.classList.add('hidden');
          modal.classList.remove('hidden');
        }
      }

      // Función para cerrar el modal de autenticación
      function closeAuthModal() {
        const modal = document.getElementById('auth-modal');
        if (modal) {
          modal.classList.add('hidden');
        }
      }

      // Función para cambiar entre login y registro
      function switchToRegister() {
        const loginForm = document.getElementById('login-form');
        const registerForm = document.getElementById('register-form');

        if (loginForm && registerForm) {
          loginForm.classList.add('hidden');
          registerForm.classList.remove('hidden');
        }
      }

      function switchToLogin() {
        const loginForm = document.getElementById('login-form');
        const registerForm = document.getElementById('register-form');

        if (loginForm && registerForm) {
          registerForm.classList.add('hidden');
          loginForm.classList.remove('hidden');
        }
      }

      // Función para iniciar sesión con Google
      async function signInWithGoogle() {
        if (!supabase) {
          console.error('🔴 Supabase no está inicializado');
          showErrorMessage('Error de configuración: Supabase no está inicializado');
          return;
        }

        try {
          console.log('🚀 Iniciando autenticación con Google...');
          console.log('🌐 URL actual:', window.location.href);
          console.log('🔗 Redirect URL que se enviará:', window.location.origin);

          const { data, error } = await supabase.auth.signInWithOAuth({
            provider: 'google',
            options: {
              redirectTo: window.location.origin,
              queryParams: {
                access_type: 'offline',
                prompt: 'consent',
              }
            }
          });

          if (error) {
            console.error('❌ Error al iniciar sesión con Google:', error);
            console.error('📋 Detalles del error:', {
              message: error.message,
              status: error.status,
              statusText: error.statusText
            });
            showErrorMessage('Error al iniciar sesión con Google: ' + error.message);
          } else {
            console.log('✅ Redirección a Google iniciada');
            console.log('📊 Datos de respuesta:', data);
            // La redirección se maneja automáticamente
          }
        } catch (error) {
          console.error('💥 Error inesperado:', error);
          showErrorMessage('Error al iniciar sesión: ' + error.message);
        }
      }

      // Función para cerrar sesión
      async function logout() {
        if (!supabase) {
          console.error('🔴 Supabase no está inicializado');
          return;
        }

        try {
          console.log('👋 Cerrando sesión...');

          const { error } = await supabase.auth.signOut();

          if (error) {
            console.error('❌ Error al cerrar sesión:', error);
            showErrorMessage('Error al cerrar sesión: ' + error.message);
          } else {
            console.log('✅ Sesión cerrada exitosamente');
            currentUser = null;
            updateUserInterface(false);
          }
        } catch (error) {
          console.error('💥 Error inesperado al cerrar sesión:', error);
          showErrorMessage('Error al cerrar sesión: ' + error.message);
        }
      }

      // Función para mostrar mensajes de error
      function showErrorMessage(message) {
        console.error('🚨 Mostrando error al usuario:', message);

        // Por ahora usamos alert, pero se puede mejorar con un modal personalizado
        alert(message);

        // También podríamos mostrar el modal de error genérico
        // const errorModal = document.getElementById('error-modal');
        // if (errorModal) {
        //   errorModal.classList.remove('hidden');
        // }
      }

      // Función para verificar si el usuario está autenticado
      function isUserAuthenticated() {
        const authenticated = currentUser !== null;
        console.log('🔍 Verificando autenticación:', authenticated ? '✅ Autenticado' : '❌ No autenticado');
        return authenticated;
      }

      // Función para obtener el token de acceso actual
      async function getCurrentAccessToken() {
        if (!supabase) {
          console.log('🔴 No se puede obtener token: Supabase no está inicializado');
          return null;
        }

        try {
          const { data: { session }, error } = await supabase.auth.getSession();

          if (error) {
            console.error('❌ Error al obtener token:', error);
            return null;
          }

          if (session && session.access_token) {
            console.log('🔑 Token de acceso obtenido exitosamente');
            console.log('⏰ Token expira en:', new Date(session.expires_at * 1000));
            return session.access_token;
          } else {
            console.log('❌ No hay token de acceso disponible');
            return null;
          }
        } catch (error) {
          console.error('💥 Error inesperado al obtener token:', error);
          return null;
        }
      }

      // Cerrar el modal al hacer clic fuera del contenido
      document.addEventListener('click', function(e) {
        const modal = document.getElementById('auth-modal');
        const modalContent = modal ? modal.querySelector('.modal-content') : null;

        if (modal && !modal.classList.contains('hidden') && e.target === modal) {
          closeAuthModal();
        }
      });

      // Función para debuggear desde la consola
      window.debugSupabase = async function() {
        console.log('🔧 === DEBUG SUPABASE ===');
        console.log('🔍 Cliente Supabase:', supabase ? 'Inicializado' : 'No inicializado');

        if (!supabase) {
          console.log('❌ Supabase no está inicializado');
          return;
        }

        try {
          // Verificar configuración
          console.log('⚙️ Configuración:');
          console.log('- URL:', supabase.supabaseUrl);
          console.log('- Key:', supabase.supabaseKey ? 'Presente' : 'Ausente');

          // Verificar usuario actual
          const { data: { user }, error: userError } = await supabase.auth.getUser();
          console.log('👤 Usuario actual:', user);
          if (userError) console.log('❌ Error usuario:', userError);

          // Verificar sesión actual
          const { data: { session }, error: sessionError } = await supabase.auth.getSession();
          console.log('📋 Sesión actual:', session);
          if (sessionError) console.log('❌ Error sesión:', sessionError);

        } catch (error) {
          console.error('💥 Error en debug:', error);
        }
        console.log('🔧 === FIN DEBUG ===');
      };

      // Función para forzar la obtención del usuario
      window.forceGetUser = async function() {
        console.log('🔄 === FORZANDO OBTENCIÓN DE USUARIO ===');

        if (!supabase) {
          console.log('❌ Supabase no está inicializado');
          return;
        }

        try {
          // Método 1: getUser
          console.log('📋 Método 1: getUser()');
          const { data: { user }, error: userError } = await supabase.auth.getUser();
          if (user) {
            console.log('✅ Usuario obtenido:', user);
            currentUser = user;
            updateUserInterface(true, user);
            return user;
          } else {
            console.log('❌ No se obtuvo usuario:', userError);
          }

          // Método 2: getSession
          console.log('📋 Método 2: getSession()');
          const { data: { session }, error: sessionError } = await supabase.auth.getSession();
          if (session && session.user) {
            console.log('✅ Usuario desde sesión:', session.user);
            currentUser = session.user;
            updateUserInterface(true, session.user);
            return session.user;
          } else {
            console.log('❌ No se obtuvo sesión:', sessionError);
          }

          // Método 3: refreshSession
          console.log('📋 Método 3: refreshSession()');
          const refreshResult = await refreshUserSession();
          if (refreshResult) {
            return currentUser;
          }

          console.log('❌ Todos los métodos fallaron');
          return null;

        } catch (error) {
          console.error('💥 Error al forzar obtención:', error);
          return null;
        }
      };



      // Función para limpiar URL inmediatamente (ejecutar antes que todo)
      function cleanUrlImmediately() {
        const url = new URL(window.location);
        if (url.searchParams.has('lang')) {
          console.log('🧹 Limpiando parámetro lang de la URL inmediatamente...');
          url.searchParams.delete('lang');
          window.history.replaceState({}, document.title, url.pathname + url.search);
        }
      }

      // Inicializar aplicación cuando se cargue la página
      window.addEventListener('load', async function() {
        // El idioma se inicializa automáticamente en language.js

        <?php if (isSupabaseConfigured()): ?>
        console.log('🚀 Inicializando aplicación...');

        // Inicializar Supabase
        initSupabase('<?php echo $supabaseConfig['url']; ?>', '<?php echo $supabaseConfig['anonKey']; ?>');

        // Configurar listener de cambios de autenticación
        setupAuthListener();

        // Verificar estado de autenticación inicial
        await checkAuthState();

        console.log('✅ Aplicación inicializada correctamente');
        console.log('💡 Tip: Ejecuta debugSupabase() en la consola para más información');
        <?php else: ?>
        console.warn('⚠️ Supabase no está configurado. Por favor, configura las credenciales en include/supabase_config.php');
        console.log('📝 Para configurar Supabase:');
        console.log('1. Edita el archivo include/supabase_config.php');
        console.log('2. Reemplaza TU_SUPABASE_URL_AQUI con tu URL de Supabase');
        console.log('3. Reemplaza TU_SUPABASE_ANON_KEY_AQUI con tu clave anónima');
        <?php endif; ?>
      });
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <div class="progress-bar" id="progress-bar"></div>

    <!-- Pantalla de carga -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="spinner mb-4"></div>
        <p class="text-white text-lg font-medium mb-1">Generando prompt con IA</p>
        <p class="text-gray-300 text-sm">Por favor espera un momento...</p>
    </div>

    <!-- Navegación -->
    <nav class="bg-white shadow-sm sticky top-0 z-10 dark:bg-gray-800">

<?php require './include/nav.php';        ?>

<!-- Menú móvil desplegable -->

<?php require './include/nav_movil.php';      ?>
    </nav>

    <div class="container mx-auto px-4 py-4 min-h-[calc(100vh-4rem)]">
        <!-- Contenedor principal con sistema de historial -->
        <div class="history-system-container relative max-w-6xl mx-auto">
            <!-- Caja de historial (inicialmente pequeña a la izquierda) -->
            <div id="history-box" class="history-box history-collapsed">
                <div class="history-header">
                    <h3 class="history-title"><?php echo t('history'); ?></h3>
                    <button id="history-toggle" class="history-toggle-btn">
                        <!-- Icono de historial cuando está colapsado -->
                        <svg class="w-4 h-4 history-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <!-- Flecha para expandir/colapsar -->
                        <svg class="w-4 h-4 expand-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
                <div class="history-content">
                    <div class="history-list">
                        <!-- El historial se cargará aquí dinámicamente -->
                        <div class="history-placeholder text-center py-8">
                            <svg class="w-12 h-12 mx-auto mb-3 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="text-gray-600 dark:text-gray-400 text-sm font-medium mb-1"><?php echo t('history_empty'); ?></p>
                            <p class="text-gray-500 dark:text-gray-500 text-xs"><?php echo t('history_empty_description'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contenedor principal de herramientas (inicialmente en el centro) -->
            <div id="main-tools-container" class="main-tools-container main-tools-normal">
                <div class="max-w-2xl mx-auto">
            <!-- Texto animado -->
            <div class="text-center mb-6">
                <h1 class="text-black dark:text-white text-4xl font-black not-italic leading-tight"><?php echo t('main_heading'); ?></h1>
                <div class="h-8">
                    <span class="text-blue-600 dark:text-blue-400 text-xl font-medium typewriter"><?php echo t('sub_heading'); ?></span>
                </div>
            </div>

            <!-- El botón flotante ha sido eliminado y reemplazado por botones dentro de cada slide -->

            <!-- Botón móvil fijo (solo visible en móviles) -->
            <button id="mobile-back-button" class="mobile-back-button" aria-label="Volver" style="display: none;">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </button>

            <div class="slide-container rounded-xl shadow-md overflow-hidden relative bg-white dark:bg-gray-800">
                <div class="slides">
                    <!-- Slide 1: Tipo de Herramienta -->
                    <div class="slide active" data-step="1" style="display: flex;">
                        <h2 class="text-3xl font-bold mb-5 text-center"><?php echo t('which_ai_tool'); ?></h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-xl mx-auto">
                            <div class="option-card rounded-xl shadow-sm" data-value="codigo">
                                <div class="text-3xl mb-3">💻</div>
                                <h3 class="text-xl font-semibold mb-2"><?php echo t('code_tool_title'); ?></h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm"><?php echo t('code_tool_description'); ?></p>
                            </div>
                            <div class="option-card rounded-xl shadow-sm" data-value="texto">
                                <div class="text-3xl mb-3">📝</div>
                                <h3 class="text-xl font-semibold mb-2"><?php echo t('writing_tool_title'); ?></h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm"><?php echo t('writing_tool_description'); ?></p>
                            </div>
                        </div>

                        <!-- Checkbox de políticas -->
                        <div class="mt-4 mb-0 flex items-start checkbox-container">
                            <input type="checkbox" id="policy-checkbox" class="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="policy-checkbox" class="ml-2 block text-sm text-gray-600 dark:text-gray-300">
                                <?php echo t('policy_checkbox'); ?>
                            </label>
                        </div>
                    </div>

                    <!-- Slide 2: Herramienta Específica (Código) -->
                    <div class="slide" data-step="2" data-type="codigo" style="display: none;">
                        <button id="back-button-2" class="back-button-inline" aria-label="Volver">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                            <span>Volver</span>
                        </button>
                        <h2 class="text-3xl font-bold mb-5 text-center">¿Qué herramienta utilizarás?</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-3xl mx-auto w-full">
                            <div class="option-card rounded-xl shadow-sm" data-value="v0">
                                <h3 class="text-xl font-semibold mb-2 dark:text-white">v0.dev</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">Generador de interfaces con React y Tailwind</p>
                            </div>
                            <div class="option-card rounded-xl shadow-sm" data-value="lovable">
                                <h3 class="text-xl font-semibold mb-2 dark:text-white">Lovable.dev</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">Creación de aplicaciones web completas</p>
                            </div>
                            <div class="option-card rounded-xl shadow-sm" data-value="cursor">
                                <h3 class="text-xl font-semibold mb-2 dark:text-white">Cursor</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">Creación de aplicaciones web completas</p>
                            </div>
                            <div class="option-card rounded-xl shadow-sm" data-value="bolt.new">
                                <h3 class="text-xl font-semibold mb-2 dark:text-white">Bolt.new</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">Creación de aplicaciones web completas</p>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 2 Alt: Herramienta Específica (Texto) -->
                    <div class="slide" data-step="2" data-type="texto" style="display: none;">
                        <button id="back-button-3" class="back-button-inline" aria-label="Volver">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                            <span>Volver</span>
                        </button>
                        <h2 class="text-3xl font-bold mb-5 text-center">¿Qué herramienta utilizarás?</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-3xl mx-auto w-full">
                            <div class="option-card rounded-xl shadow-sm" data-value="jasper">
                                <h3 class="text-xl font-semibold mb-2 dark:text-white">Jasper</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">IA para escritura creativa y marketing</p>
                            </div>
                            <div class="option-card rounded-xl shadow-sm" data-value="copy">
                                <h3 class="text-xl font-semibold mb-2 dark:text-white">Copy.ai</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">Generación de contenido y copywriting</p>
                            </div>
                            <div class="option-card rounded-xl shadow-sm" data-value="writesonic">
                                <h3 class="text-xl font-semibold mb-2 dark:text-white">Writesonic</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">Generación de contenido y copywriting</p>
                            </div>
                            <div class="option-card rounded-xl shadow-sm" data-value="claude o chatgpt">
                                <h3 class="text-xl font-semibold mb-2 dark:text-white">Claude o chatgpt</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">Generación de contenido y copywriting</p>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 3: Características (Código) -->
                    <div class="slide" data-step="3" data-form="codigo" style="display: none;">
                        <button id="back-button-codigo" class="back-button-inline" aria-label="Volver">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                            <span><?php echo t('form_back'); ?></span>
                        </button>
                        <div class="max-w-xl mx-auto w-full">
                            <h2 class="text-3xl font-bold mb-4 text-center dark:text-white"><?php echo t('form_describe_project'); ?></h2>
                            <form id="project-form-codigo" class="space-y-4">
                                <div class="form-group">
                                    <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2"><?php echo t('form_project_type_label'); ?></label>
                                    <input type="text" name="projectType" class="input-large" placeholder="<?php echo t('form_project_type_placeholder'); ?>" data-autofocus required>
                                </div>
                                <div class="form-group">
                                    <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2"><?php echo t('form_features_label'); ?></label>
                                    <textarea name="features" class="input-large" rows="2" placeholder="<?php echo t('form_features_placeholder'); ?>" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2"><?php echo t('form_style_label'); ?></label>
                                    <input type="text" name="style" class="input-large" placeholder="<?php echo t('form_style_placeholder'); ?>" required>
                                </div>
                                <div class="flex justify-center mt-6">
                                    <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors text-base font-medium shadow-lg hover:shadow-xl">
                                        <?php echo t('form_generate_prompt'); ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Slide 3: Características (Texto) -->
                    <div class="slide" data-step="3" data-form="texto" style="display: none;">
                        <button id="back-button-texto" class="back-button-inline" aria-label="Volver">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                            <span>Volver</span>
                        </button>
                        <div class="max-w-xl mx-auto w-full">
                            <h2 class="text-3xl font-bold mb-4 text-center dark:text-white"><?php echo t('form_describe_content'); ?></h2>
                            <form id="project-form-texto" class="space-y-4">
                                <div class="form-group">
                                    <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2"><?php echo t('form_content_type_label'); ?></label>
                                    <input type="text" name="contentType" class="input-large" placeholder="<?php echo t('form_content_type_placeholder'); ?>" data-autofocus required>
                                </div>
                                <div class="form-group">
                                    <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2"><?php echo t('form_main_topic_label'); ?></label>
                                    <textarea name="mainTopic" class="input-large" rows="2" placeholder="<?php echo t('form_main_topic_placeholder'); ?>" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2"><?php echo t('form_tone_label'); ?></label>
                                    <input type="text" name="tone" class="input-large" placeholder="<?php echo t('form_tone_placeholder'); ?>" required>
                                </div>
                                <div class="flex justify-center mt-6">
                                    <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors text-base font-medium shadow-lg hover:shadow-xl">
                                        <?php echo t('form_generate_prompt'); ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Slide Final: Resultado -->
                    <div class="slide" data-step="4" style="display: none;">
                        <button id="back-button-final" class="back-button-inline" aria-label="Volver">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                            <span>Volver</span>
                        </button>
                        <div class="max-w-xl mx-auto w-full">
                            <h2 class="text-3xl font-bold mb-4 text-center">Tu Prompt está Listo</h2>
                            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-xl shadow-inner">
                                <pre id="final-prompt" class="whitespace-pre-wrap text-base leading-relaxed dark:text-gray-200"></pre>
                            </div>
                            <div class="mt-6 flex justify-center space-x-4">
                                <button id="back-to-form" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                    Volver
                                </button>
                                <button id="copy-prompt" class="bg-blue-600 text-white px-5 py-2 rounded-lg hover:bg-blue-700 transition-colors text-base font-medium shadow-md hover:shadow-lg">
                                    Copiar Prompt
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php require './include/footer.php'; ?>

    <!-- Modal de feedback con Tally -->
    <?php require './include/tally_modal.php';  ?>

    <!-- Modal de límite excedido -->

<?php require './include/modal_limite_excedido.php'; ?>

    <!-- Modal de error genérico -->
  <?php require './include/modal_error.php'; ?>

    <!-- Modal de autenticación con Supabase -->
    <div id="auth-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="modal-content bg-white dark:bg-gray-800 rounded-lg max-w-md w-full mx-4 relative">
            <button onclick="closeAuthModal()" class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 z-10">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>

            <!-- Formulario de inicio de sesión -->
            <div id="login-form" class="p-6">
                <h2 class="text-2xl font-bold text-center mb-6 text-gray-900 dark:text-white"><?php echo t('nav_login'); ?></h2>

                <button onclick="signInWithGoogle()" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                    <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <?php echo $current_language === 'en' ? 'Continue with Google' : 'Continuar con Google'; ?>
                </button>

                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        <?php echo $current_language === 'en' ? "Don't have an account?" : '¿No tienes una cuenta?'; ?>
                        <button onclick="switchToRegister()" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 font-medium ml-1">
                            <?php echo t('nav_signup'); ?>
                        </button>
                    </p>
                </div>
            </div>

            <!-- Formulario de registro -->
            <div id="register-form" class="p-6 hidden">
                <h2 class="text-2xl font-bold text-center mb-6 text-gray-900 dark:text-white"><?php echo t('nav_signup'); ?></h2>

                <button onclick="signInWithGoogle()" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                    <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <?php echo $current_language === 'en' ? 'Sign up with Google' : 'Registrarse con Google'; ?>
                </button>

                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        <?php echo $current_language === 'en' ? 'Already have an account?' : '¿Ya tienes una cuenta?'; ?>
                        <button onclick="switchToLogin()" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 font-medium ml-1">
                            <?php echo t('nav_login'); ?>
                        </button>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="./js/app.js"></script>

    <!-- Script para manejar los modales -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Modal de Tally
            const closeModalBtn = document.getElementById('close-tally-modal');
            const tallyModal = document.getElementById('tally-feedback-modal');

            if (closeModalBtn && tallyModal) {
                closeModalBtn.addEventListener('click', function() {
                    tallyModal.classList.add('hidden');
                });

                // Cerrar modal al hacer clic fuera del contenido
                tallyModal.addEventListener('click', function(e) {
                    if (e.target === tallyModal) {
                        tallyModal.classList.add('hidden');
                    }
                });
            }

            // Modal de límite excedido
            const closeRateLimitBtn = document.getElementById('close-rate-limit-modal');
            const rateLimitOkBtn = document.getElementById('rate-limit-ok-button');
            const rateLimitModal = document.getElementById('rate-limit-modal');

            if (closeRateLimitBtn && rateLimitModal) {
                closeRateLimitBtn.addEventListener('click', function() {
                    rateLimitModal.classList.add('hidden');
                });

                if (rateLimitOkBtn) {
                    rateLimitOkBtn.addEventListener('click', function() {
                        rateLimitModal.classList.add('hidden');
                    });
                }

                // Cerrar modal al hacer clic fuera del contenido
                rateLimitModal.addEventListener('click', function(e) {
                    if (e.target === rateLimitModal) {
                        rateLimitModal.classList.add('hidden');
                    }
                });
            }

            // Modal de error genérico
            const closeErrorBtn = document.getElementById('close-error-modal');
            const errorOkBtn = document.getElementById('error-ok-button');
            const errorModal = document.getElementById('error-modal');

            if (closeErrorBtn && errorModal) {
                closeErrorBtn.addEventListener('click', function() {
                    errorModal.classList.add('hidden');
                });

                if (errorOkBtn) {
                    errorOkBtn.addEventListener('click', function() {
                        errorModal.classList.add('hidden');
                    });
                }

                // Cerrar modal al hacer clic fuera del contenido
                errorModal.addEventListener('click', function(e) {
                    if (e.target === errorModal) {
                        errorModal.classList.add('hidden');
                    }
                });
            }
        });
    </script>
    <script src="./js/movilMenu.js"></script>
</body>
</html>
