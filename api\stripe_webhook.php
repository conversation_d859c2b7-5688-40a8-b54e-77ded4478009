<?php
require_once __DIR__ . '/../utils/stripe_helper.php';
require_once __DIR__ . '/../config/database.php';

// Configurar headers para webhook
header('Content-Type: application/json');

// Obtener el payload y la firma
$payload = @file_get_contents('php://input');
$signature = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? '';

// Secreto del endpoint (deberás configurarlo en Stripe Dashboard)
$endpointSecret = $_ENV['STRIPE_WEBHOOK_SECRET'] ?? '';

if (empty($endpointSecret)) {
    error_log('STRIPE_WEBHOOK_SECRET no configurado');
    http_response_code(400);
    echo json_encode(['error' => 'Webhook no configurado']);
    exit;
}

// Validar el webhook
$webhookResult = StripeHelper::validateWebhook($payload, $signature, $endpointSecret);

if (!$webhookResult['success']) {
    error_log('Webhook validation failed: ' . $webhookResult['error']);
    http_response_code(400);
    echo json_encode(['error' => $webhookResult['error']]);
    exit;
}

$event = $webhookResult['event'];

try {
    // Manejar diferentes tipos de eventos
    switch ($event->type) {
        case 'checkout.session.completed':
            handleCheckoutSessionCompleted($event->data->object);
            break;
            
        case 'payment_intent.succeeded':
            handlePaymentIntentSucceeded($event->data->object);
            break;
            
        default:
            error_log('Unhandled event type: ' . $event->type);
    }
    
    http_response_code(200);
    echo json_encode(['received' => true]);
    
} catch (Exception $e) {
    error_log('Webhook error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * Manejar checkout completado
 */
function handleCheckoutSessionCompleted($session) {
    error_log('Checkout session completed: ' . $session->id);
    
    // Obtener metadata
    $userId = $session->metadata->user_id ?? null;
    $credits = (int)($session->metadata->credits ?? 0);
    
    if (!$userId || !$credits) {
        error_log('Missing metadata in session: ' . json_encode($session->metadata));
        return;
    }
    
    // Agregar créditos al usuario
    addCreditsToUser($userId, $credits, $session->id);
}

/**
 * Manejar pago exitoso
 */
function handlePaymentIntentSucceeded($paymentIntent) {
    error_log('Payment intent succeeded: ' . $paymentIntent->id);
    // Aquí puedes agregar lógica adicional si es necesaria
}

/**
 * Agregar créditos al usuario en la base de datos
 */
function addCreditsToUser($userId, $credits, $sessionId) {
    try {
        $pdo = getDBConnection();
        
        // Iniciar transacción
        $pdo->beginTransaction();
        
        // Verificar si ya se procesó esta transacción
        $checkStmt = $pdo->prepare("
            SELECT id FROM credit_transactions 
            WHERE stripe_session_id = ? AND user_id = ?
        ");
        $checkStmt->execute([$sessionId, $userId]);
        
        if ($checkStmt->fetch()) {
            error_log("Transaction already processed: $sessionId");
            $pdo->rollback();
            return;
        }
        
        // Agregar créditos al usuario
        $updateStmt = $pdo->prepare("
            UPDATE users 
            SET credits = credits + ? 
            WHERE supabase_user_id = ?
        ");
        $updateStmt->execute([$credits, $userId]);
        
        // Registrar la transacción
        $transactionStmt = $pdo->prepare("
            INSERT INTO credit_transactions 
            (user_id, credits, transaction_type, stripe_session_id, created_at) 
            VALUES (?, ?, 'purchase', ?, NOW())
        ");
        $transactionStmt->execute([$userId, $credits, $sessionId]);
        
        // Confirmar transacción
        $pdo->commit();
        
        error_log("Successfully added $credits credits to user $userId");
        
    } catch (Exception $e) {
        $pdo->rollback();
        error_log('Error adding credits: ' . $e->getMessage());
        throw $e;
    }
}
