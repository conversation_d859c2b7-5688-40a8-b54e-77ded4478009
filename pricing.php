<?php
require_once 'include/language.php';
require_once 'include/user_manager.php';

$userManager = new UserManager();
$pricingPlans = $userManager->getPricingPlans();
?>
<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('pricing_title'); ?> - FastlyAI</title>
    
    <!-- ⚡ Script INLINE para evitar flash del modo oscuro -->
    <script>
        (function() {
            if (localStorage.getItem('darkMode') === 'enabled' || 
                (localStorage.getItem('darkMode') === null && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                document.documentElement.classList.add('dark');
            }
        })();
    </script>
    
    <!-- Script para aplicar el modo oscuro inmediatamente -->
    <script type="module" src="./js/colorMode.js"></script>

    <!-- CSS personalizado -->
    <link rel="stylesheet" href="./css/styles.css">
    <link rel="stylesheet" href="./css/custom.css">


    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body class="h-full bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Navegación -->
    <nav class="bg-white shadow-sm sticky top-0 z-10 dark:bg-gray-800">
        <?php include 'include/nav.php'; ?>

        <!-- Menú móvil desplegable -->
        <?php include 'include/nav_movil.php'; ?>
    </nav>
    
    <main class="pt-20">
        <!-- Hero Section -->
        <div class="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 py-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                    <?php echo t('pricing_hero_title'); ?>
                </h1>
                <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                    <?php echo t('pricing_hero_subtitle'); ?>
                </p>
                
                <!-- Contador de créditos del usuario (si está logueado) -->
                <div id="user-credits-display" class="hidden bg-white dark:bg-gray-800 rounded-lg p-4 inline-block shadow-lg border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo t('your_credits'); ?></p>
                            <p id="user-credits-count" class="text-2xl font-bold text-blue-600 dark:text-blue-400">0</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Pricing Plans -->
        <div class="py-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <?php foreach ($pricingPlans as $plan): ?>
                    <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-8 <?php echo $plan['is_popular'] ? 'ring-2 ring-blue-500 scale-105' : ''; ?>">
                        <?php if ($plan['is_popular']): ?>
                        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                            <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                                <?php echo t('most_popular'); ?>
                            </span>
                        </div>
                        <?php endif; ?>
                        
                        <div class="text-center">
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                <?php echo htmlspecialchars($plan['name']); ?>
                            </h3>
                            
                            <div class="mb-6">
                                <span class="text-4xl font-bold text-gray-900 dark:text-white">
                                    $<?php echo number_format($plan['price_usd'], 0); ?>
                                </span>
                                <span class="text-gray-500 dark:text-gray-400 ml-2">USD</span>
                            </div>
                            
                            <div class="mb-6">
                                <div class="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                                    <?php echo number_format($plan['credits']); ?>
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <?php echo t('credits'); ?>
                                </div>
                                <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                    $<?php echo number_format($plan['price_per_credit'], 3); ?> <?php echo t('per_credit'); ?>
                                </div>
                            </div>
                            
                            <button
                                data-credits="<?php echo $plan['credits']; ?>"
                                data-amount="<?php echo $plan['price_usd']; ?>"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 <?php echo $plan['is_popular'] ? 'bg-blue-600 hover:bg-blue-700' : ''; ?> disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <?php echo t('buy_credits'); ?>
                            </button>
                        </div>
                        
                        <div class="mt-8 space-y-3">
                            <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                <svg class="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <?php echo t('feature_ai_prompts'); ?>
                            </div>
                            <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                <svg class="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <?php echo t('feature_no_expiry'); ?>
                            </div>
                            <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                <svg class="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <?php echo t('feature_all_tools'); ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- FAQ Section -->
                <div class="mt-20">
                    <h2 class="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
                        <?php echo t('faq_title'); ?>
                    </h2>
                    
                    <div class="max-w-3xl mx-auto space-y-6">
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                                <?php echo t('faq_what_are_credits'); ?>
                            </h3>
                            <p class="text-gray-600 dark:text-gray-300">
                                <?php echo t('faq_credits_answer'); ?>
                            </p>
                        </div>
                        
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                                <?php echo t('faq_credits_expire'); ?>
                            </h3>
                            <p class="text-gray-600 dark:text-gray-300">
                                <?php echo t('faq_no_expiry_answer'); ?>
                            </p>
                        </div>
                        
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                                <?php echo t('faq_refund_policy'); ?>
                            </h3>
                            <p class="text-gray-600 dark:text-gray-300">
                                <?php echo t('faq_refund_answer'); ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <?php include 'include/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="js/auth.js"></script>
    <script src="./js/language.js"></script>
    <script src="./js/pricing.js"></script>

    <script>
        // Pasar las traducciones del modo oscuro al JavaScript
        window.translations = {
            dark_mode_text: <?php echo json_encode(t('dark_mode_text')); ?>,
            light_mode_text: <?php echo json_encode(t('light_mode_text')); ?>
        };

        // Funciones globales para la autenticación (compatibilidad con nav.php)
        function showLoginModal() {
            if (window.authSystem) {
                window.authSystem.showLoginModal();
            }
        }

        function showRegisterModal() {
            if (window.authSystem) {
                window.authSystem.showRegisterModal();
            }
        }
    </script>

    <!-- Scripts de Stripe -->
    <script src="https://js.stripe.com/v3/"></script>
    <script src="./js/stripe.js"></script>
    <script src="./js/movilMenu.js"></script>
</body>
</html>
